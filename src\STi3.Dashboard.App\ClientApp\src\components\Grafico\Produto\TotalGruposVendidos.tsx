import React, { useState, useEffect, useCallback } from 'react';
import HeaderCard from 'components/HeaderCard';
import { useChartData } from 'store/ChartDataContext';
import IdentificacaoGrafico from 'constants/identificacaoGrafico';
import GraficoSemDados from 'components/GraficoSemDados';
import TotalizadorDividido from 'components/TotalizadorDividido';
import Totalizador from 'components/Totalizador';
import DatePickerRange from 'components/DatePickerRange';
import dateToDayDatePicker from 'helpers/dateFormat';
import { DayRange } from 'react-modern-calendar-datepicker';

import formatarDinheiro from 'helpers/moneyFormat';
import { Divider, makeStyles } from '@material-ui/core';
import { maxHourDate, minHourDate } from 'helpers/dateMinMax';
import auth from 'auth';
import TipoSistemaEnum from 'constants/enumTipoSistema';

const sistema = auth.getSistema() || '';

const sistemaIsZendarOrPowerstock = sistema.value !== TipoSistemaEnum.STI3;

const useStyles = makeStyles(() => ({
  container: {
    background: '#4B3D7B',
    padding: '5px 10px 0px 10px',
    margin: '0px 10px 0px 10px',
  },
  observacao: {
    padding: '0px 10px 5px 10px',
    fontSize: '12px',
  },
  divider: {
    margin: '0px 10px 0px 10px',
    background: '#7C66C8',
  },
  porcentagem: {
    display: 'flex',
    alignItems: 'center',
    height: '100%',
    fontWeight: 'bold',
  },
  descricao: {
    color: (props: any) => props.cor,
    fontSize: '14px',
    paddingBottom: '5px 0 5px 0',
    display: 'flex',
    justifyContent: 'space-between',
    fontWeight: 'bold',
  },
  obs: {
    color: (props: any) => props.cor,
    fontSize: '12px',
    paddingBottom: '5px 0 5px 0',
    display: 'flex',
    justifyContent: 'space-between',
  },
  quantidade: {
    fontSize: '12px',
    marginBottom: '5px',
    minWidth: '80px',
    textAlign: 'right',
    fontWeight: 'normal',
  },
  valorTotal: {
    fontSize: '12px',
    marginBottom: '5px',
    minWidth: '80px',
    textAlign: 'right',
  },
}));

const TotalGruposVendidos = () => {
  const { chartData } = useChartData();
  const [dataUltimaSincronizacao, setDataUltimaSincronizacao] = useState('');
  const [totalVendas, setTotalVendas] = useState(0);
  const [totalVendasAdicional, setTotalVendasAdicional] = useState(0);
  const [defaultDateRange, setDefaultDateRange] = useState<DayRange>({
    from: dateToDayDatePicker(new Date()),
    to: dateToDayDatePicker(new Date()),
  });

  const [legenda, setLegenda] = useState([] as Array<any>);

  const obterDadosGrafico = useCallback((dados) => {
    const informacoesFiltro = dados.filter(
      (item) =>
        item.identificacao === IdentificacaoGrafico.Produtos_TotalGruposVendidos
    );

    if (informacoesFiltro.length > 0) {
      return {
        ...informacoesFiltro[0],
      };
    }

    return null;
  }, []);

  const obterDadosPorFiltro = useCallback(
    (dtInicial, dtFinal) => {
      const informacao = obterDadosGrafico(chartData);

      const informacoesFiltro = informacao.dados.filter((item) => {
        const dataItem = new Date(item.Data);
        return (
          dtInicial &&
          dtFinal &&
          dataItem >= minHourDate(dtInicial) &&
          dataItem <= maxHourDate(dtFinal)
        );
      });

      let valTotal = 0;
      let valTotalAdicional = 0;

      let listItens = Array<any>();

      for (let index = 0; index < informacoesFiltro.length; index += 1) {
        const infoFiltro = informacoesFiltro[index];
        valTotal += infoFiltro.Total;
        valTotalAdicional += infoFiltro.TotalAdicional;

        for (
          let indexDado = 0;
          indexDado < infoFiltro.Dados.length;
          indexDado += 1
        ) {
          const dado = infoFiltro.Dados[indexDado];

          const objIndex = listItens.findIndex(
            (obj) =>
              obj.descricao === dado.Descricao &&
              obj.obs === dado.InformacaoAdicional[0].Descricao
          );

          if (objIndex === -1) {
            listItens.push({
              id: listItens.length - 1,
              descricao: dado.Descricao,
              obs: dado.InformacaoAdicional[0].Descricao,
              valorTotal: dado.Total,
              quantidade: dado.InformacaoAdicional[0].Total,
            });
          } else {
            const itemUpdate = listItens[objIndex];

            listItens[objIndex] = {
              id: itemUpdate.id,
              descricao: dado.Descricao,
              obs: dado.InformacaoAdicional[0].Descricao,
              valorTotal: itemUpdate.valorTotal + dado.Total,
              quantidade:
                itemUpdate.quantidade + dado.InformacaoAdicional[0].Total,
            };
          }
        }
      }

      setTotalVendas(valTotal);
      setTotalVendasAdicional(valTotalAdicional);

      listItens = listItens.sort(
        (a, b) => parseFloat(b.quantidade) - parseFloat(a.quantidade)
      );

      setLegenda(listItens);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [chartData]
  );

  const resetGrafico = () => {
    setTotalVendas(0);
  };

  useEffect(() => {
    const informacao = obterDadosGrafico(chartData);

    if (informacao) {
      const dateTo = new Date(informacao.dataUltimaSincronizacao);
      const dateFrom = new Date(informacao.dataUltimaSincronizacao);

      setDataUltimaSincronizacao(dateTo.toLocaleString());

      setDefaultDateRange({
        from: dateToDayDatePicker(dateFrom),
        to: dateToDayDatePicker(dateTo),
      });

      const filtros = informacao.dados.map((item, index) => {
        return {
          texto: item.Data,
          valor: item.Total,
          possuiDados: item.Total > 0,
          key: index,
        };
      });

      if (filtros) {
        const filtrosComValor = filtros.filter((filtro) => filtro.possuiDados);
        if (filtrosComValor.length > 0) {
          obterDadosPorFiltro(dateFrom, dateTo);
        } else {
          resetGrafico();
        }
      }
    } else {
      resetGrafico();
    }
  }, [chartData, obterDadosPorFiltro, obterDadosGrafico]);

  const classes = useStyles({ cor: 'white' });

  const dateOnChange = (selectedDate: { from: Date; to: Date }) => {
    obterDadosPorFiltro(selectedDate.from, selectedDate.to);
  };

  const InfoGrafico = (props) => {
    const { descricao, obs, valorTotal, quantidade } = props;

    return (
      <>
        <div className={classes.container}>
          <div className={classes.descricao}>
            <div>{descricao}</div>
            {!sistemaIsZendarOrPowerstock && (
              <div className={classes.valorTotal}>
                {formatarDinheiro(valorTotal, true)}
              </div>
            )}
            {sistemaIsZendarOrPowerstock && (
              <div className={classes.valorTotal}>{valorTotal}</div>
            )}
          </div>
          <div
            style={{
              paddingBottom: '5px 0 5px 0',
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <div className={classes.obs}>{obs}</div>
            {!sistemaIsZendarOrPowerstock && (
              <div className={classes.quantidade}>
                <p>{quantidade}</p>
              </div>
            )}
            {sistemaIsZendarOrPowerstock && (
              <div className={classes.quantidade}>
                <p>{formatarDinheiro(quantidade, true)}</p>
              </div>
            )}
          </div>
        </div>
        <Divider className={classes.divider} />
      </>
    );
  };
  return (
    <>
      <HeaderCard
        titulo="Vendas por Grupo"
        atualizadoEm={dataUltimaSincronizacao}
        exibirFiltro={false}
        handleOrdem={() => {}}
      />

      <DatePickerRange
        bgButtonColor="#7C66C8"
        bgDateColor="#4B3D7B"
        onChange={dateOnChange}
        defaultDateRange={defaultDateRange}
      />

      {
        // TODO Rever regra após atualização dos clientes
        totalVendasAdicional ? (
          <>
            <TotalizadorDividido
              valorEsquerdo={totalVendasAdicional.toFixed(2)}
              tituloEsquerdo="Quantidade Total"
              exibirEmReaisEsquerdo={false}
              valorDireito={totalVendas}
              tituloDireito="Valor Total"
              exibirEmReaisDireito
            />
          </>
        ) : (
          <Totalizador
            valor={totalVendas}
            titulo="Total de Itens Vendidos"
            exibirEmReais={false}
          />
        )
      }

      <div style={{ marginBottom: '4px' }}>
        {totalVendas === 0 && <GraficoSemDados />}
        {totalVendas > 0 && (
          <div style={{ paddingBottom: '5px' }}>
            {legenda.map((item) => (
              <InfoGrafico
                key={item.id}
                descricao={item.descricao}
                valorTotal={item.valorTotal}
                quantidade={
                  item.quantidade % 1 > 0
                    ? item.quantidade.toFixed(2)
                    : item.quantidade
                }
              />
            ))}
          </div>
        )}
      </div>
    </>
  );
};

export default TotalGruposVendidos;
